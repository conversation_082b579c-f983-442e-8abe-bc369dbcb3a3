# Timeline workflows

Use the Timeline window to create Timeline assets and Timeline instances that you can then use to record animation, schedule animation, nest Timeline instances to create cinematic content, place markers that emit signals, and customize Timeline with custom tracks, clips, and markers.

To successfully perform these tasks requires following steps in a particular order. Each workflow in this section demonstrate how to perform specific tasks.

| **Workflow** | **Demonstrates** |
| :-------------------- | :----------------------- |
| [Create a Timeline asset and Timeline instance](wf-create-instance.md) | How to create a Timeline asset and a Timeline instance, and how to associate a Timeline instance with a GameObject. This is the first step when using Timeline to create a cut-scene or gameplay animation. |
| [Record basic animation](wf-record-anim.md) | How to record keyframe animation directly to a Timeline instance using an Animation track and an Infinite clip. |
| [Convert an Infinite clip](wf-convert-infinite.md) | How to convert an Infinite clip to an Animation clip. |
| [Animate a humanoid](wf-anim-human.md) | How to animate a humanoid model to follow two Animation clips. This workflow also demonstrates how to blend clips, match clips, and how to manually reduce foot sliding. |
| [Override an animation with an Avatar Mask](wf-anim-override.md) | How to use an Animation Override track and an Avatar Mask to replace only the upper-body animation in an Animation track. |
| [Create a Sub-Timeline instance](wf-subtimeline.md) | How to create and use a Sub-Timeline instance to combine two Timeline instances into a single cut-scene. |
| [Use markers and signals for footsteps](wf-signals.md) | How to add Timeline markers, signal emitters, and signal receivers to play one of two audio sources when a character's foot contacts the floor. |
| [Create a custom Notes marker](wf-custom-marker.md) | How to create a custom marker that you can use to add notes to your Timeline instances. This workflow also demonstrates how to change the default appearance of a custom marker with scripting and a Unity Style Sheet (USS). |
