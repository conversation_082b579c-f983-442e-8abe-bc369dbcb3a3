// Forward pass
Pass
{
    Tags { "LightMode"="${VFXURPForwardPassName}"}

    ${VFXStencilForward}

    HLSLPROGRAM
    #pragma target 4.5
    ${VFXPassForwardLitAdditionalPragma}
    ${VFXURPForwardDefines}

    ${VFXIncludeRP("VFXLitVaryings.template")}
	${VFXIncludeRP("VFXVertexProbeSampling.template"),VFX_MATERIAL_TYPE_SIX_WAY_SMOKE}

    struct ps_input
    {
        float4 pos : SV_POSITION;

        ${VFXURPLitDeclareVaryings}

        #if USE_FLIPBOOK_INTERPOLATION
        float4 uv : TEXCOORD1;
        #else
        #if USE_FLIPBOOK_ARRAY_LAYOUT
        float3 uv : TEXCOORD1;
        #else
        float2 uv : TEXCOORD1;
        #endif
        #endif
        #if USE_SOFT_PARTICLE || USE_ALPHA_TEST || USE_FLIPBOOK_INTERPOLATION || VFX_FEATURE_MOTION_VECTORS_FORWARD
        // x: inverse soft particles fade distance
        // y: alpha threshold
        // z: frame blending factor
        VFX_OPTIONAL_INTERPOLATION float3 builtInInterpolants : TEXCOORD2;
        #endif

        #if USE_FLIPBOOK_MOTIONVECTORS
        // x: motion vector scale u
        // y: motion vector scale v
        VFX_OPTIONAL_INTERPOLATION float2 builtInInterpolants2 : TEXCOORD3;
        #endif

        VFX_OPTIONAL_INTERPOLATION float3 normal : TEXCOORD4;
        #if USE_NORMAL_MAP || USE_NORMAL_BENDING || SHADERGRAPH_NEEDS_TANGENT_FORWARD || VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
        VFX_OPTIONAL_INTERPOLATION float3 tangent : TEXCOORD5;
        #endif
        #if USE_NORMAL_BENDING
        float2 bentFactors : TEXCOORD6;
        #endif

        float3 posWS : TEXCOOR9; // Needed for fog

        ${VFXAdditionalInterpolantsDeclaration}

        UNITY_VERTEX_OUTPUT_STEREO
        VFX_VERTEX_OUTPUT_INSTANCE_INDEX
    };

${VFXURPLitVaryingsMacros}

#if VFX_MATERIAL_TYPE_SIX_WAY_SMOKE
${VFXVertexProbeVaryingsMacros}
#endif

#define VFX_VARYING_PS_INPUTS ps_input
#define VFX_VARYING_POSCS pos
#define VFX_VARYING_INVSOFTPARTICLEFADEDISTANCE builtInInterpolants.x
#define VFX_VARYING_ALPHATHRESHOLD builtInInterpolants.y
#define VFX_VARYING_FRAMEBLEND builtInInterpolants.z
#define VFX_VARYING_MOTIONVECTORSCALE builtInInterpolants2.xy
#define VFX_VARYING_UV uv
#define VFX_VARYING_NORMAL normal
#if USE_NORMAL_MAP || USE_NORMAL_BENDING || SHADERGRAPH_NEEDS_TANGENT_FORWARD || VFX_MATERIAL_TYPE_SIX_WAY_SMOKE

#define VFX_VARYING_TANGENT tangent
#endif
#if USE_NORMAL_BENDING
#define VFX_VARYING_BENTFACTORS bentFactors
#endif
#define VFX_VARYING_POSWS posWS

${VFXVertexProbeDeclareFunctions}


${VFXBegin:VFXVertexAdditionalProcess}
${VFXURPLitFillVaryings}
${VFXVertexProbeFillVaryings}
${VFXEnd}

    ${VFXInclude("Shaders/ParticlePlanarPrimitives/Pass.template")}
    #define SHADERPASS SHADERPASS_FORWARD
    ${VFXIncludeRP("VFXLit.template")}

    ${SHADERGRAPH_PIXEL_CODE_FORWARD}

    #pragma fragment frag
    void frag(ps_input i
    , out float4 outColor : SV_Target0
    #if USE_DOUBLE_SIDED
    , bool frontFace : SV_IsFrontFace
    #endif
    #if VFX_FEATURE_MOTION_VECTORS_FORWARD
    , out float4 outMotionVector : SV_Target1
    #endif
    )
    {
        UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
        VFXTransformPSInputs(i);
        VFX_FRAG_SETUP_INSTANCE_ID(i);
        ${VFXComputeNormalWS}
#if !USE_DOUBLE_SIDED
		const bool frontFace = true;
#endif
        #ifdef VFX_SHADERGRAPH
            ${VFXAdditionalInterpolantsPreparation}
            ${SHADERGRAPH_PIXEL_CALL_FORWARD}

            ${VFXIncludeRP("VFXSGSurfaceData.template")}
            outColor = VFXGetPixelOutputForwardShaderGraph(i, surface, normalWS);
        #else
            outColor = VFXGetPixelOutputForward(i, normalWS, uvData, frontFace);
        #endif
    }
    ENDHLSL
}
