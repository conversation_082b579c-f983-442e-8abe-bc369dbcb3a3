Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 64661 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-06T21:20:45Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
-logFile
Logs/AssetImportWorker6.log
-srvPort
51478
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [28452]  Target information:

Player connection [28452]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 2850702635 [EditorId] 2850702635 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28452]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 2850702635 [EditorId] 2850702635 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [28452] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     22612 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56724
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002861 seconds.
- Loaded All Assemblies, in  0.299 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 171 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.441 seconds
Domain Reload Profiling: 739ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (139ms)
		LoadAssemblies (91ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (136ms)
			TypeCache.Refresh (134ms)
				TypeCache.ScanAssembly (120ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (441ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (233ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (85ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.545 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 1134ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (251ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (177ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (120ms)
			BuildScriptInfoCaches (35ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (591ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (459ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (122ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.1 MB). Loaded Objects now: 6634.
Memory consumption went from 159.1 MB to 153.1 MB.
Total: 11.062800 ms (FindLiveObjects: 1.027300 ms CreateObjectMapping: 0.484200 ms MarkObjects: 6.122700 ms  DeleteObjects: 3.427800 ms)

========================================================================
Received Import Request.
  Time since last request: 30176.204883 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Table_Furn_3.prefab
  artifactKey: Guid(cf4d1460232059540bdbbc50ded5b42a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Table_Furn_3.prefab using Guid(cf4d1460232059540bdbbc50ded5b42a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d807485789e6a3c498e3ededd1a6375') in 2.039982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 276.655996 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '092f7974db3fb175701bdf9959c1e23b') in 0.0005749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 11.058311 seconds.
  path: Assets/Scenes/MainLevel.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/MainLevel.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa7ad041820cffd7415cc36faeeffccd') in 0.0004309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.953067 seconds.
  path: Assets/Scenes/MainLevel.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/MainLevel.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acd60ff6bff7d1e2c59b9f6d09e2a060') in 0.0958067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 492.774534 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0004289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.230287 seconds.
  path: Assets/Prefabs
  artifactKey: Guid(2d0d0c1ecf2ce4f4bb6d8ef63030221a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Prefabs using Guid(2d0d0c1ecf2ce4f4bb6d8ef63030221a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd40c9e58248e6a1e11528e7bc7b29a3a') in 0.0099915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.933353 seconds.
  path: Assets/Prefabs
  artifactKey: Guid(2d0d0c1ecf2ce4f4bb6d8ef63030221a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs using Guid(2d0d0c1ecf2ce4f4bb6d8ef63030221a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc1a04b96a39d104a25c184c0bd44466') in 0.0004037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.142511 seconds.
  path: Assets/Prefabs/Chunk Prefab.prefab
  artifactKey: Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Chunk Prefab.prefab using Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20c29ad940abe64f5a6e3ce3b4d8fe30') in 1.125092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 33.539779 seconds.
  path: Assets/Scripts
  artifactKey: Guid(e596837ae6213ac48bfcb2ec6e6d1e97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts using Guid(e596837ae6213ac48bfcb2ec6e6d1e97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '15e2e4b0bedb3bb71e56398fa203cf6a') in 0.0105773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.677119 seconds.
  path: Assets/Scripts
  artifactKey: Guid(e596837ae6213ac48bfcb2ec6e6d1e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts using Guid(e596837ae6213ac48bfcb2ec6e6d1e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd20cbe9831b3b2f4e4cbee9cf8ef6b0') in 0.0003827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5969 unused Assets / (6.2 MB). Loaded Objects now: 6791.
Memory consumption went from 145.2 MB to 139.1 MB.
Total: 12.475700 ms (FindLiveObjects: 1.054100 ms CreateObjectMapping: 0.301200 ms MarkObjects: 7.835900 ms  DeleteObjects: 3.283500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 17.565964 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0e776cd7e50cc9e1ffde17c4c7587e2f') in 0.0767491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.619 seconds
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1322ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (359ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (142ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (558ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 38 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.4 MB). Loaded Objects now: 6684.
Memory consumption went from 154.1 MB to 147.7 MB.
Total: 10.404400 ms (FindLiveObjects: 1.003400 ms CreateObjectMapping: 0.354500 ms MarkObjects: 5.135900 ms  DeleteObjects: 3.909500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5.296142 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6fd1f61c333b96d7c0e9b321a7d7da9b') in 0.0064845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.583 seconds
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.747 seconds
Domain Reload Profiling: 1330ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (126ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (109ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (747ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (608ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.0 MB). Loaded Objects now: 6686.
Memory consumption went from 152.4 MB to 146.4 MB.
Total: 10.432100 ms (FindLiveObjects: 1.012000 ms CreateObjectMapping: 0.315600 ms MarkObjects: 5.647800 ms  DeleteObjects: 3.455700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.522 seconds
Refreshing native plugins compatible for Editor in 1.06 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.681 seconds
Domain Reload Profiling: 1203ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (325ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (79ms)
				TypeCache.ScanAssembly (70ms)
			BuildScriptInfoCaches (63ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (681ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (547ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (141ms)
			ProcessInitializeOnLoadAttributes (334ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (4.9 MB). Loaded Objects now: 6688.
Memory consumption went from 152.3 MB to 147.4 MB.
Total: 11.874000 ms (FindLiveObjects: 1.063900 ms CreateObjectMapping: 0.354700 ms MarkObjects: 6.567100 ms  DeleteObjects: 3.887600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (6.1 MB). Loaded Objects now: 6688.
Memory consumption went from 152.7 MB to 146.6 MB.
Total: 10.607400 ms (FindLiveObjects: 1.064200 ms CreateObjectMapping: 0.366600 ms MarkObjects: 5.796300 ms  DeleteObjects: 3.379500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 862.352578 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2577a3f5fd9dc7ce097affb09da5c76d') in 0.0182754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.580 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.565 seconds
Domain Reload Profiling: 1145ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (268ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (565ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (446ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (250ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (4.9 MB). Loaded Objects now: 6690.
Memory consumption went from 152.5 MB to 147.6 MB.
Total: 10.492600 ms (FindLiveObjects: 1.334600 ms CreateObjectMapping: 0.370600 ms MarkObjects: 5.621500 ms  DeleteObjects: 3.165100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.547 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 1142ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (136ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (596ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (266ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (5.9 MB). Loaded Objects now: 6692.
Memory consumption went from 152.6 MB to 146.7 MB.
Total: 10.127900 ms (FindLiveObjects: 1.052600 ms CreateObjectMapping: 0.368200 ms MarkObjects: 5.439900 ms  DeleteObjects: 3.266300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (6.0 MB). Loaded Objects now: 6692.
Memory consumption went from 152.7 MB to 146.7 MB.
Total: 12.122100 ms (FindLiveObjects: 1.170200 ms CreateObjectMapping: 1.238700 ms MarkObjects: 5.871400 ms  DeleteObjects: 3.840900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.586 seconds
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.528 seconds
Domain Reload Profiling: 1113ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (364ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (149ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (430ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.1 MB). Loaded Objects now: 6694.
Memory consumption went from 152.6 MB to 146.5 MB.
Total: 10.009800 ms (FindLiveObjects: 1.023000 ms CreateObjectMapping: 0.373500 ms MarkObjects: 5.199100 ms  DeleteObjects: 3.413400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5339.244766 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Palette-Material.mat
  artifactKey: Guid(b20396b4a8fc9ea4598cbb3f8d4ff60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Palette-Material.mat using Guid(b20396b4a8fc9ea4598cbb3f8d4ff60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3482d91ad6fa28a5085246801928d428') in 0.2805517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.630 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.658 seconds
Domain Reload Profiling: 1288ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (295ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (146ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (129ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (658ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (307ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5976 unused Assets / (5.9 MB). Loaded Objects now: 6749.
Memory consumption went from 153.0 MB to 147.2 MB.
Total: 10.545400 ms (FindLiveObjects: 1.014100 ms CreateObjectMapping: 0.317200 ms MarkObjects: 5.594000 ms  DeleteObjects: 3.617700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.580 seconds
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.628 seconds
Domain Reload Profiling: 1209ms
	BeginReloadAssembly (171ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (159ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (629ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (508ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (284ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.1 MB). Loaded Objects now: 6751.
Memory consumption went from 152.9 MB to 146.8 MB.
Total: 10.696400 ms (FindLiveObjects: 1.087300 ms CreateObjectMapping: 0.368200 ms MarkObjects: 5.383200 ms  DeleteObjects: 3.857000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 728.412010 seconds.
  path: Assets/Scenes/MainLevel.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/MainLevel.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7512af0e2ea34087621f20ebdfa4a110') in 0.0087481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 8.694778 seconds.
  path: Assets/Prefabs/Chunk Prefab.prefab
  artifactKey: Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Chunk Prefab.prefab using Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d9cdb67ae2673c55216a7788c03ada3') in 0.2644185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 109.132177 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e5f7756282b5916a73b20ab1631af2b7') in 0.0009168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 689.292924 seconds.
  path: Packages/com.unity.render-pipelines.universal/Textures/BlueNoise64/L/LDR_LLL1_0.png
  artifactKey: Guid(e3d24661c1e055f45a7560c033dbb837) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.universal/Textures/BlueNoise64/L/LDR_LLL1_0.png using Guid(e3d24661c1e055f45a7560c033dbb837) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fd164d9ce24adbf05d740b3b820e926') in 0.020869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.001492 seconds.
  path: Packages/com.unity.render-pipelines.universal/Textures/BayerMatrix.png
  artifactKey: Guid(f9ee4ed84c1d10c49aabb9b210b0fc44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.universal/Textures/BayerMatrix.png using Guid(f9ee4ed84c1d10c49aabb9b210b0fc44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f4eaafe6ae676af10278a2b8ab982ea9') in 0.009753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.575 seconds
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.530 seconds
Domain Reload Profiling: 1105ms
	BeginReloadAssembly (163ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (358ms)
		LoadAssemblies (237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (83ms)
				TypeCache.ScanAssembly (72ms)
			BuildScriptInfoCaches (88ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (530ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (432ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (121ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (5.9 MB). Loaded Objects now: 6777.
Memory consumption went from 153.2 MB to 147.4 MB.
Total: 10.307400 ms (FindLiveObjects: 1.116800 ms CreateObjectMapping: 0.373200 ms MarkObjects: 5.352100 ms  DeleteObjects: 3.464500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1715.579634 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'adc8a71bd0a68fc9a3e5ae6fdf168881') in 0.0053159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.594 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.603 seconds
Domain Reload Profiling: 1198ms
	BeginReloadAssembly (170ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (367ms)
		LoadAssemblies (284ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (136ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (485ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (257ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.1 MB). Loaded Objects now: 6779.
Memory consumption went from 153.1 MB to 147.0 MB.
Total: 11.650400 ms (FindLiveObjects: 1.060400 ms CreateObjectMapping: 0.875200 ms MarkObjects: 5.717700 ms  DeleteObjects: 3.996100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 892.396688 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cefcc8f6d2051a919301664561137941') in 0.0069453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

