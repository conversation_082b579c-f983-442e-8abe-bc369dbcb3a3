Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 64661 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-06T21:17:44Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
-logFile
Logs/AssetImportWorker2.log
-srvPort
51478
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [27884]  Target information:

Player connection [27884]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 898994342 [EditorId] 898994342 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27884]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 898994342 [EditorId] 898994342 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [27884] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     22612 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56080
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002809 seconds.
- Loaded All Assemblies, in  0.245 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 174 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.409 seconds
Domain Reload Profiling: 653ms
	BeginReloadAssembly (79ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (103ms)
		LoadAssemblies (78ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (100ms)
			TypeCache.Refresh (99ms)
				TypeCache.ScanAssembly (89ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (409ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (376ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (226ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (75ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.503 seconds
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.519 seconds
Domain Reload Profiling: 1020ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (332ms)
		LoadAssemblies (236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (155ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (103ms)
			BuildScriptInfoCaches (32ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (520ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (401ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (243ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (5.6 MB). Loaded Objects now: 6633.
Memory consumption went from 156.9 MB to 151.4 MB.
Total: 10.955500 ms (FindLiveObjects: 1.083000 ms CreateObjectMapping: 0.483000 ms MarkObjects: 5.972900 ms  DeleteObjects: 3.415700 ms)

========================================================================
Received Import Request.
  Time since last request: 30083.762122 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f14b68bc9245f4badc5e0a8f06fd9696') in 0.1099982 seconds
Number of updated asset objects reloaded before import = 20Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.697379 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Rock_Small_Env_03.prefab
  artifactKey: Guid(ea25b10905178f7438b99d067166f765) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Rock_Small_Env_03.prefab using Guid(ea25b10905178f7438b99d067166f765) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e42bb0c3a88ae0f06a237d5b01020e4') in 0.0403305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets
  artifactKey: Guid(5390f65f0ddbe8147a640a88de127c93) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets using Guid(5390f65f0ddbe8147a640a88de127c93) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da2b668736d225cf9c2aa5371b7e09ab') in 0.0121039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Animations/Vil_Animation_Controller.controller
  artifactKey: Guid(efdf566a075231a4880b5f044d4e639c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Animations/Vil_Animation_Controller.controller using Guid(efdf566a075231a4880b5f044d4e639c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a9afc7285dfbd04087e7681803f74e53') in 0.013455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Rock_Small_Env_03.fbx
  artifactKey: Guid(f3462c1d89d0ad646945e748e66d59a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Rock_Small_Env_03.fbx using Guid(f3462c1d89d0ad646945e748e66d59a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'adcee95221f9b8c082b7b25f3b9867b4') in 0.0327491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Barrel_Wooden_Prop.prefab
  artifactKey: Guid(01b0d7cd3288fb04aa0c7ed55acafb11) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Barrel_Wooden_Prop.prefab using Guid(01b0d7cd3288fb04aa0c7ed55acafb11) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '567266a9eb4043a397105e27f4784d86') in 0.0166127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs
  artifactKey: Guid(8340c32a4a00fa8439d457737dd28dc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs using Guid(8340c32a4a00fa8439d457737dd28dc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a419f2b312f05602a9f17eb6b6e9b33') in 0.0078221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/SM_Applesm.prefab
  artifactKey: Guid(37cc5698a53a6bf478be4c88a8e64e9c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/SM_Applesm.prefab using Guid(37cc5698a53a6bf478be4c88a8e64e9c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'daa18a49322e2d1ac109fd0cbbbd7d60') in 0.0112465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/SM_Powerup_Coin_Gold.fbx
  artifactKey: Guid(e5b208f628a6a7d4898c966321977228) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/SM_Powerup_Coin_Gold.fbx using Guid(e5b208f628a6a7d4898c966321977228) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd3101e92449fb0147ce45b9947a1200') in 0.017043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000010 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Flag_Prop.fbx
  artifactKey: Guid(d56a180f0cddbcd42bf7bf7cecfdadfa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Flag_Prop.fbx using Guid(d56a180f0cddbcd42bf7bf7cecfdadfa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0c0db72ed47bd60fb10f1a1afc7bf49') in 0.0177212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Beam_Bldg_01.fbx
  artifactKey: Guid(f78258d2f086db141a2cc64f53d26f01) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Beam_Bldg_01.fbx using Guid(f78258d2f086db141a2cc64f53d26f01) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '358ad81c06c691e109c664511ff2ccd9') in 0.0145626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/M_Coin.mat
  artifactKey: Guid(895e7a4bfb7039e40ac54e86d9785961) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/M_Coin.mat using Guid(895e7a4bfb7039e40ac54e86d9785961) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3534a802f70abdf3d20e8d53652cc7d8') in 0.0244901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/LayoutGround.mat
  artifactKey: Guid(9667f45405701664692342f822f1f027) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/LayoutGround.mat using Guid(9667f45405701664692342f822f1f027) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '435c2cf6b24cbc221b6a00a6decbcdc6') in 0.0113539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Cannon_Weap.fbx
  artifactKey: Guid(8217adedfdf948541b3f4e53854b4d88) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Cannon_Weap.fbx using Guid(8217adedfdf948541b3f4e53854b4d88) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13e9c7cf54f8e6c5bbe73d6cf81ddf2f') in 0.0168998 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Stool_Stone_Furn.fbx
  artifactKey: Guid(68fdca81b4574914ba90548cd37c0e9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Stool_Stone_Furn.fbx using Guid(68fdca81b4574914ba90548cd37c0e9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de82409bc082401e36dda4d69c224186') in 0.0196108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Wooden_Beam_Prop_07.fbx
  artifactKey: Guid(53206b7a30e661c4db1afed096c4fff7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Wooden_Beam_Prop_07.fbx using Guid(53206b7a30e661c4db1afed096c4fff7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f2448c5ce506d8ac2fd809fd02df6f8') in 0.0149672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Flag_Prop.prefab
  artifactKey: Guid(68a853dabcdaa3445ab666d8badab23f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Flag_Prop.prefab using Guid(68a853dabcdaa3445ab666d8badab23f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '07ffc85885cdc690d697a4c50afc652e') in 0.0127363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/T_Coins_CubeVillage.png
  artifactKey: Guid(b3386079529843c4198115538afb68a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/T_Coins_CubeVillage.png using Guid(b3386079529843c4198115538afb68a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc9319b9a81998443fa0a37276371c72') in 0.0359284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Banner_Pole_Prop.prefab
  artifactKey: Guid(dd319dcc6b26fce498151719fb190433) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Banner_Pole_Prop.prefab using Guid(dd319dcc6b26fce498151719fb190433) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac74059fd3e79f92711dac18f3c4eb76') in 0.0160787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Imported Assets/GDTV Assets/Images
  artifactKey: Guid(36eb8ccbbca0b1449b49063dd0b3326e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Images using Guid(36eb8ccbbca0b1449b49063dd0b3326e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c73eeed8c35ee12a3b4a457ff8a23be3') in 0.0094669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000010 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Chest_Prop.fbx
  artifactKey: Guid(11b864780b0f3af4092d3e5228f59fbd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Chest_Prop.fbx using Guid(11b864780b0f3af4092d3e5228f59fbd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e66b3fddea70d4b258ccc66f97189c9') in 0.018718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Coping_Wall_Bldg_06.fbx
  artifactKey: Guid(d8c04aefe5b1d764c9831e09f859fe37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Coping_Wall_Bldg_06.fbx using Guid(d8c04aefe5b1d764c9831e09f859fe37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '35b070baddaab86daec074a92fe8dc87') in 0.0204532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Stool_Stone_Furn.prefab
  artifactKey: Guid(caaa734de3fb1bc4788057fd8d11d6f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Stool_Stone_Furn.prefab using Guid(caaa734de3fb1bc4788057fd8d11d6f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0049eb8ff8aed5c5b7b314a3511156c3') in 0.0153001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Beam_Bldg_01.prefab
  artifactKey: Guid(c57ad1e69ca25e6438069c7a5200f235) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Beam_Bldg_01.prefab using Guid(c57ad1e69ca25e6438069c7a5200f235) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3483e85679c248b2e09a0da470a53d49') in 0.0170492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Pillar_Bldg_01.prefab
  artifactKey: Guid(0d8ae2724d639a34eabff1d210146d9c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Pillar_Bldg_01.prefab using Guid(0d8ae2724d639a34eabff1d210146d9c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6139cb0e30fa040771f5336c0199ac50') in 0.0140492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Emission.png
  artifactKey: Guid(87a2fb48c1f035247816574d433fbe12) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Emission.png using Guid(87a2fb48c1f035247816574d433fbe12) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '479bc6c0db91d89dc1cd3709db7bb80a') in 0.0165397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Fence_Lattice_Env_3.fbx
  artifactKey: Guid(7f3c62b05a61522409bde59e829d1510) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Fence_Lattice_Env_3.fbx using Guid(7f3c62b05a61522409bde59e829d1510) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d429718bb55e562582b3343a1b32e3b') in 0.0198496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Imported Assets/GDTV Assets
  artifactKey: Guid(ddb0ead78b84c794ba7608be25dde30b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets using Guid(ddb0ead78b84c794ba7608be25dde30b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f1e73993a5e53d2fb2e13adc736515f') in 0.0090245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/T_VegFruitA_CubeVillage.png
  artifactKey: Guid(0bde90d96a0405d429473faf1dc7d39d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/T_VegFruitA_CubeVillage.png using Guid(0bde90d96a0405d429473faf1dc7d39d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4f8940055032460a1b3c17377c061587') in 0.0192551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Cart_Vehc.prefab
  artifactKey: Guid(7c241ce7df21bd34199a7f79cae0a014) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Cart_Vehc.prefab using Guid(7c241ce7df21bd34199a7f79cae0a014) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70d398f89acb943c65b6c0d53e77d8a7') in 0.0188878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Animations/Run.anim
  artifactKey: Guid(01c2846014b42854993a9669acb51e81) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Animations/Run.anim using Guid(01c2846014b42854993a9669acb51e81) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd024349a3f05dcd31bfee11b2633089') in 0.0235032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/King.prefab
  artifactKey: Guid(dcbc89efbe5bd174b99e07c76bd0b6bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/King.prefab using Guid(dcbc89efbe5bd174b99e07c76bd0b6bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7dbf46068a9297ee1bad3e5c1cc9e0e8') in 0.0139884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 136

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Imported Assets/GDTV Assets/Images/ParticleCloudWhiteMine.png
  artifactKey: Guid(157867080761dec42af74d7f307c11fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Images/ParticleCloudWhiteMine.png using Guid(157867080761dec42af74d7f307c11fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '66f8fd64b53a97133fb67f6d31932ece') in 0.0157038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Bench_Wooden_Furn_03.fbx
  artifactKey: Guid(bf0db8c628ff09745993f5f06350f88c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Bench_Wooden_Furn_03.fbx using Guid(bf0db8c628ff09745993f5f06350f88c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6f7d751b147b830e3e0d28a79a3cc2e1') in 0.0176601 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000010 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures
  artifactKey: Guid(d9b7949786886ca4fba99ad5d601822f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures using Guid(d9b7949786886ca4fba99ad5d601822f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c5610b2e91d44fc64cd68b1a62ac85d') in 0.0085965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Table_Furn_3.prefab
  artifactKey: Guid(cf4d1460232059540bdbbc50ded5b42a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Table_Furn_3.prefab using Guid(cf4d1460232059540bdbbc50ded5b42a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5dc6dc2886e35bbf989cc090df4635f9') in 0.0148618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Wooden_Beam_Prop_07.prefab
  artifactKey: Guid(1344acf7c5f71a4489cc0546666b72a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Wooden_Beam_Prop_07.prefab using Guid(1344acf7c5f71a4489cc0546666b72a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fddc23f23b72ed1decee36f639b815a7') in 0.0128105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Wall_Cornice_Modular_01.fbx
  artifactKey: Guid(2a51ab4348072a541aa0f8aea0e6f88c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Wall_Cornice_Modular_01.fbx using Guid(2a51ab4348072a541aa0f8aea0e6f88c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79b65f2d63efa2160cd14fb30f66853e') in 0.0169528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Cart_Vehc.fbx
  artifactKey: Guid(3e7cf6513ce9e2942b2dc28456fa9062) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Cart_Vehc.fbx using Guid(3e7cf6513ce9e2942b2dc28456fa9062) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '52104f298359b4eff2e3f701cb2ad9e5') in 0.0237722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models
  artifactKey: Guid(3aae3fdf25db654498b049320b5a920f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models using Guid(3aae3fdf25db654498b049320b5a920f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9fee9157e532e728d2174e358cf87bab') in 0.0090386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Audio
  artifactKey: Guid(90bd480ccedab2f4d9e01e22a1897e81) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Audio using Guid(90bd480ccedab2f4d9e01e22a1897e81) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb4e6abbcf2162c1ae73f3ee2cd05bc6') in 0.0075989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Table_Furn_3.fbx
  artifactKey: Guid(395c2c0d2a168dc4088d77209dc4c6dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Table_Furn_3.fbx using Guid(395c2c0d2a168dc4088d77209dc4c6dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f555464ae791b82de46bf9c08c53fbee') in 0.0217301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0