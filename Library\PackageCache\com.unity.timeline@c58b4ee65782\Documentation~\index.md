# Unity's Timeline

Use Unity's Timeline to create cinematic content, gameplay sequences, audio sequences, and complex particle effects.

Each cut-scene, cinematic, or gameplay sequence that you create with Unity's Timeline consists of a Timeline asset and a Timeline instance. The [Timeline window](tl-window.md) creates and modifies Timeline assets and Timeline instances simultaneously.

The [Timeline assets and instances topic](tl-overview.md) provides details on the relationship between the Timeline window, Timeline assets, and Timeline instances.

The [Timeline Workflows section](wf-overview.md) provides basic steps on how to create Timeline assets and Timeline instances, record basic animation, animate humanoids, use Animation Override tracks, Sub-Timelines, and other Timeline features.

The [Timeline Samples section](samp-overview.md) provides a description of the samples included in the Timeline package.
