#pragma target 4.5

#if defined(WRITE_NORMAL_BUFFER)
#define NEEDS_NORMAL 1
#elif (SHADERPASS == SHADERPASS_DEPTHONLY)
#define NEEDS_NORMAL SHADERGRAPH_NEEDS_NORMAL_DEPTHONLY
#elif (SHADERPASS == SHADERPASS_DEPTHNORMALSONLY)
#define NEEDS_NORMAL SHADERGRAPH_NEEDS_NORMAL_DEPTHNORMALS
#else
#define NEEDS_NORMAL 0
#endif

#if defined(WRITE_NORMAL_BUFFER)
#define NEEDS_TANGENT 1
#elif (SHADERPASS == SHADERPASS_DEPTHONLY)
#define NEEDS_TANGENT SHADERGRAPH_NEEDS_TANGENT_DEPTHONLY
#elif (SHADERPASS == SHADERPASS_DEPTHNORMALSONLY)
#define NEEDS_TANGENT SHADERGRAPH_NEEDS_TANGENT_DEPTHNORMALS
#else
#define NEEDS_TANGENT 0
#endif

struct ps_input
{        
    float4 pos : SV_POSITION;
    #if USE_FLIPBOOK_INTERPOLATION
        float4 uv : TEXCOORD0;
    #else
        #if USE_FLIPBOOK_ARRAY_LAYOUT
        float3 uv : TEXCOORD0;
        #else
        float2 uv : TEXCOORD0;
        #endif    
    #endif
    #if VFX_SHADERGRAPH_HAS_UV1
    float4 uv1 : COLOR2;
    #endif
    #if VFX_SHADERGRAPH_HAS_UV2
    float4 uv2 : COLOR3;
    #endif
    #if VFX_SHADERGRAPH_HAS_UV3
    float4 uv3 : COLOR4;
    #endif
    #if VFX_SHADERGRAPH_HAS_COLOR
    float4 vertexColor : COLOR1;
    #endif
    #if USE_ALPHA_TEST || USE_FLIPBOOK_INTERPOLATION || VFX_USE_ALPHA_CURRENT
    // x: alpha threshold
    // y: frame blending factor
    // z: alpha
    // w: smoothness
    nointerpolation float4 builtInInterpolants : TEXCOORD1;
    #endif
    #if USE_FLIPBOOK_MOTIONVECTORS
    // x: motion vector scale u
    // y: motion vector scale v
    nointerpolation float2 builtInInterpolants2 : TEXCOORD3;
    #endif
    #if NEEDS_NORMAL
    float3 normal : TEXCOORD4;
    #if NEEDS_TANGENT
    float4 tangent : TEXCOORD5;
    #endif
    #endif

    #if VFX_NEEDS_POSWS_INTERPOLATOR
    float3 posWS : TEXCOORD6;
    #endif

    #if VFX_PASSDEPTH == VFX_PASSDEPTH_MOTION_VECTOR
    VFX_DECLARE_MOTION_VECTORS_STORAGE(7,8)
    #endif

    ${VFXAdditionalInterpolantsDeclaration}
    
    UNITY_VERTEX_OUTPUT_STEREO
    VFX_VERTEX_OUTPUT_INSTANCE_INDEX
};

#define VFX_VARYING_PS_INPUTS ps_input
#define VFX_VARYING_POSCS pos
#define VFX_VARYING_ALPHA builtInInterpolants.z
#define VFX_VARYING_ALPHATHRESHOLD builtInInterpolants.x
#define VFX_VARYING_FRAMEBLEND builtInInterpolants.y
#define VFX_VARYING_MOTIONVECTORSCALE builtInInterpolants2.xy
#define VFX_VARYING_UV uv

#if VFX_PASSDEPTH == VFX_PASSDEPTH_MOTION_VECTOR
#define VFX_VARYING_VELOCITY_CPOS VFX_DECLARE_MOTION_VECTORS_VARYING_NONJITTER
#define VFX_VARYING_VELOCITY_CPOS_PREVIOUS VFX_DECLARE_MOTION_VECTORS_VARYING_PREVIOUS
#endif

#if NEEDS_NORMAL
#define VFX_VARYING_NORMAL normal
#endif

#if NEEDS_TANGENT
#define VFX_VARYING_TANGENT tangent
#endif

#if VFX_NEEDS_POSWS_INTERPOLATOR
#define VFX_VARYING_POSWS posWS
#endif

${VFXBegin:VFXVertexAdditionalProcess}
${VFXURPLitFillVaryings}
${VFXEnd}

${VFXInclude("Shaders/ParticleMeshes/Pass.template")}
${VFXPassDepthCommonFragmentURPLit}
