Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 64661 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-06T21:17:44Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
-logFile
Logs/AssetImportWorker3.log
-srvPort
51478
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [28036]  Target information:

Player connection [28036]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 334289078 [EditorId] 334289078 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [28036]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 334289078 [EditorId] 334289078 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [28036] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     22612 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56444
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003729 seconds.
- Loaded All Assemblies, in  0.253 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 168 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.401 seconds
Domain Reload Profiling: 654ms
	BeginReloadAssembly (81ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (106ms)
		LoadAssemblies (80ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (102ms)
				TypeCache.ScanAssembly (91ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (401ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (222ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (37ms)
			ProcessInitializeOnLoadAttributes (77ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.499 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.532 seconds
Domain Reload Profiling: 1029ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (330ms)
		LoadAssemblies (230ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (104ms)
			BuildScriptInfoCaches (33ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (532ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (409ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (6.1 MB). Loaded Objects now: 6633.
Memory consumption went from 158.9 MB to 152.8 MB.
Total: 10.709900 ms (FindLiveObjects: 1.150100 ms CreateObjectMapping: 0.523500 ms MarkObjects: 5.819100 ms  DeleteObjects: 3.216700 ms)

========================================================================
Received Import Request.
  Time since last request: 30086.597441 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_02.prefab
  artifactKey: Guid(4c39b1f701ec2de46af88aa81689be7e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_02.prefab using Guid(4c39b1f701ec2de46af88aa81689be7e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '294a4c0ea40d53d0fce14c328511562e') in 0.1601222 seconds
Number of updated asset objects reloaded before import = 20Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Tree_Stump_Vege_01.fbx
  artifactKey: Guid(a4f488122d768b0448979e7e0a066590) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Tree_Stump_Vege_01.fbx using Guid(a4f488122d768b0448979e7e0a066590) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a9d974e8174a77f9b126c20deb3a1be') in 0.0316722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_03.prefab
  artifactKey: Guid(b4a8bcfa9b3dbab48bfb90252163fab0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_03.prefab using Guid(b4a8bcfa9b3dbab48bfb90252163fab0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71596659ab7ff3a707d50785bec536a8') in 0.0206066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Barrel_Wooden_Prop.fbx
  artifactKey: Guid(2d9f0fde09754f44b82875fd9e60ef24) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Barrel_Wooden_Prop.fbx using Guid(2d9f0fde09754f44b82875fd9e60ef24) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b42c9e0d0397af069487822054566182') in 0.0196741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Banner_Pole_Prop.fbx
  artifactKey: Guid(468b86ff572c21d4e93e898cacb3c423) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Banner_Pole_Prop.fbx using Guid(468b86ff572c21d4e93e898cacb3c423) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cfc8e15722e7b1bd0885714219a5ebdc') in 0.0197294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Animations
  artifactKey: Guid(f328300395ce2664a86e7dcc58db76a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Animations using Guid(f328300395ce2664a86e7dcc58db76a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8dd72a9b16c9e16252ab5116e1525525') in 0.0094507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Bench_Wooden_Furn_02.fbx
  artifactKey: Guid(6d8137bb40b67d94ba35c34c941584cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Bench_Wooden_Furn_02.fbx using Guid(6d8137bb40b67d94ba35c34c941584cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1604bb197e6c82acf764f83b0459fe44') in 0.0332454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Light_Hanging_Pole_Furn.fbx
  artifactKey: Guid(7c27ef0e3ed279242a48d522027855ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Light_Hanging_Pole_Furn.fbx using Guid(7c27ef0e3ed279242a48d522027855ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7738565ae49cbb79cbc65b1beb06b0d5') in 0.0203579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Chest_Prop.prefab
  artifactKey: Guid(7e6fb34f368d7a24e862ec8f79407d75) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Chest_Prop.prefab using Guid(7e6fb34f368d7a24e862ec8f79407d75) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd3ff1c428e048eebc9ac462728861c5') in 0.0171638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Chair_Wooden_Furn_04.fbx
  artifactKey: Guid(dffc10c2c9b1eea4b9e6fffc248e9f01) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Chair_Wooden_Furn_04.fbx using Guid(dffc10c2c9b1eea4b9e6fffc248e9f01) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fdfed05bdeb161c745ad130577b5c5b') in 0.0198494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Chair_Wooden_Furn_04.prefab
  artifactKey: Guid(fb5e4c107a8467043a7603e936d86a47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Chair_Wooden_Furn_04.prefab using Guid(fb5e4c107a8467043a7603e936d86a47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4302adbd39659721f2388d77a34ca81') in 0.0142471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/SM_Powerup_Coin_Gold.prefab
  artifactKey: Guid(ab7079beca09c4a45a24d22df07e346f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/SM_Powerup_Coin_Gold.prefab using Guid(ab7079beca09c4a45a24d22df07e346f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f02e12ab83c8182bf203cce75b5c7009') in 0.0115742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Light_Hanging_Pole_Furn.prefab
  artifactKey: Guid(9e3740eea4654ff4595607f1fa3a28c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Light_Hanging_Pole_Furn.prefab using Guid(9e3740eea4654ff4595607f1fa3a28c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96508a94c0c0144479cdbc678003df7d') in 0.0153303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Palette-Material.mat
  artifactKey: Guid(b20396b4a8fc9ea4598cbb3f8d4ff60d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-Palette-Material.mat using Guid(b20396b4a8fc9ea4598cbb3f8d4ff60d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b0210ef5fc23161e5d7e04287320abf3') in 0.0130133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Sign_Furn.prefab
  artifactKey: Guid(5e55e605674404f4ba346895e2675cbb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Sign_Furn.prefab using Guid(5e55e605674404f4ba346895e2675cbb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ee8e945e32a2c079a0dc8e522cdedcd1') in 0.014357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Path_Cobblestone_Env_03.prefab
  artifactKey: Guid(3354a2fee23c9fc45a513927cd492e15) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Path_Cobblestone_Env_03.prefab using Guid(3354a2fee23c9fc45a513927cd492e15) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd062737970f15e82f7cfbe8798d7012c') in 0.0129927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Mossy_Rock_Env_01.fbx
  artifactKey: Guid(7d679be3d3713ff458264ba8a63ec9b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Mossy_Rock_Env_01.fbx using Guid(7d679be3d3713ff458264ba8a63ec9b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08a3e0758723f601f31bfea375ea53b3') in 0.0184522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Fence_Lattice_Env_3.prefab
  artifactKey: Guid(1656d845b8440f7499036e22fd8a8999) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Fence_Lattice_Env_3.prefab using Guid(1656d845b8440f7499036e22fd8a8999) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71aac6785a6d3518670bca6d6c6289ce') in 0.0107375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Pillar_Bldg_01.fbx
  artifactKey: Guid(e6a9e85339e7fce44b2f0d4dd5044014) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Pillar_Bldg_01.fbx using Guid(e6a9e85339e7fce44b2f0d4dd5044014) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f981a2e807395cf9862b070b031b202') in 0.015942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-MetallicSmoothpng.png
  artifactKey: Guid(14ccd4af4096c524290f20c0e464c27b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/Castle-MetallicSmoothpng.png using Guid(14ccd4af4096c524290f20c0e464c27b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e160337c83fa6642a65a2153926a7e90') in 0.0372007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/castle-colour-palette.png
  artifactKey: Guid(537e3b8237afd4c4082486ad963e88a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/castle-colour-palette.png using Guid(537e3b8237afd4c4082486ad963e88a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '475fff2a22c6819086d2dd4d289f8008') in 0.0158303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Cannon_Weap.prefab
  artifactKey: Guid(0023c8b20ba66f440b40cdfb80d40b9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Cannon_Weap.prefab using Guid(0023c8b20ba66f440b40cdfb80d40b9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8b62dd1719f3167247d2bd38ff9d7bb') in 0.0158932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Mossy_Rock_Env_01.prefab
  artifactKey: Guid(94e6a88d7014ff54386296c2929cb89e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Mossy_Rock_Env_01.prefab using Guid(94e6a88d7014ff54386296c2929cb89e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcf18213c1f9dc764d7ac8f7dca4b09c') in 0.0148673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Materials and Textures/M_VF A.mat
  artifactKey: Guid(9f4e6c06a8fa3bf43bd15b12d99d65d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Materials and Textures/M_VF A.mat using Guid(9f4e6c06a8fa3bf43bd15b12d99d65d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a96553a53f26dacba07024f6ebd9cf6') in 0.012463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Images/Long Triangle.png
  artifactKey: Guid(90ae70b650dc9de4ea612dd27709e6d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Images/Long Triangle.png using Guid(90ae70b650dc9de4ea612dd27709e6d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c6b7d8cea5051c9d06eddac29292f446') in 0.0176233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/King.fbx
  artifactKey: Guid(c349c914fdd739041b58a931c9682720) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/King.fbx using Guid(c349c914fdd739041b58a931c9682720) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f28c0c378733bd6c2b2984059b2fb585') in 0.0286445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 134

========================================================================
Received Import Request.
  Time since last request: 0.000014 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Wall_Cornice_Modular_01.prefab
  artifactKey: Guid(fd377e6b49a4d1040ba1cf2817673455) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Wall_Cornice_Modular_01.prefab using Guid(fd377e6b49a4d1040ba1cf2817673455) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8a9164cd16d84b556984d6932a7cacf') in 0.0171615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Tree_Stump_Vege_01.prefab
  artifactKey: Guid(0fea0fb09c334714c91e6341b0890375) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Tree_Stump_Vege_01.prefab using Guid(0fea0fb09c334714c91e6341b0890375) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '748a3035f1ca876a2bfe95b9ee98e636') in 0.0142248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Crate_Wooden_Prop.fbx
  artifactKey: Guid(a6607eb523e0cfd4ca16eb020b458a8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Crate_Wooden_Prop.fbx using Guid(a6607eb523e0cfd4ca16eb020b458a8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a31eb7e8857dab243b8e8602df2c9a57') in 0.0174129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Crate_Wooden_Prop.prefab
  artifactKey: Guid(1dbc3380871ee554db4b0105cc963764) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Crate_Wooden_Prop.prefab using Guid(1dbc3380871ee554db4b0105cc963764) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45bbfa34e1012e9b16f5400c7055ba09') in 0.0141986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Path_Cobblestone_Env_03.fbx
  artifactKey: Guid(0912617fbeea7cc48bd07bc266eed7c6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Path_Cobblestone_Env_03.fbx using Guid(0912617fbeea7cc48bd07bc266eed7c6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3fd7af47197be4294850e444410a1115') in 0.0142957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000010 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/SM_Applesm.fbx
  artifactKey: Guid(cb51006cf02db7c4b9fbca8079ff9b9b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/SM_Applesm.fbx using Guid(cb51006cf02db7c4b9fbca8079ff9b9b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c90d35d546f30930d08a555eec0bc01b') in 0.0175888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000011 seconds.
  path: Assets/Imported Assets/GDTV Assets/Models/Sign_Furn.fbx
  artifactKey: Guid(f48f5770c9acc434eb3924b9575a45b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Models/Sign_Furn.fbx using Guid(f48f5770c9acc434eb3924b9575a45b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24448321864318b49ced0d80c1da79b7') in 0.0198898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Coping_Wall_Bldg_06.prefab
  artifactKey: Guid(5f28e8a76fb078c4ab5d1543c3a3e4ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Coping_Wall_Bldg_06.prefab using Guid(5f28e8a76fb078c4ab5d1543c3a3e4ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '153f049de0ce748cb10635e0d382b492') in 0.0141585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Imported Assets/GDTV Assets/Audio/Boulder Smash.wav
  artifactKey: Guid(b025affaac4800d46a478597f6621056) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Imported Assets/GDTV Assets/Audio/Boulder Smash.wav using Guid(b025affaac4800d46a478597f6621056) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e2b62ede8ffa1943a714fb030839a01') in 0.0484801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 84.861648 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Barrel_Wooden_Prop.prefab
  artifactKey: Guid(01b0d7cd3288fb04aa0c7ed55acafb11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Barrel_Wooden_Prop.prefab using Guid(01b0d7cd3288fb04aa0c7ed55acafb11) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb441e3b85f44a22d2efb2a4465730b0') in 1.933136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Wall_Cornice_Modular_01.prefab
  artifactKey: Guid(fd377e6b49a4d1040ba1cf2817673455) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Wall_Cornice_Modular_01.prefab using Guid(fd377e6b49a4d1040ba1cf2817673455) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67a4b5ddf1cef1f179cd1af5972ebeef') in 0.0158292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Beam_Bldg_01.prefab
  artifactKey: Guid(c57ad1e69ca25e6438069c7a5200f235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Beam_Bldg_01.prefab using Guid(c57ad1e69ca25e6438069c7a5200f235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b590556dbead870530e7b2db7ad68aee') in 0.0102298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Coping_Wall_Bldg_06.prefab
  artifactKey: Guid(5f28e8a76fb078c4ab5d1543c3a3e4ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Coping_Wall_Bldg_06.prefab using Guid(5f28e8a76fb078c4ab5d1543c3a3e4ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61afe1462671ba4a7f8762ca2ec44a77') in 0.0107874 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Cart_Vehc.prefab
  artifactKey: Guid(7c241ce7df21bd34199a7f79cae0a014) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Cart_Vehc.prefab using Guid(7c241ce7df21bd34199a7f79cae0a014) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cd8a697b5fa98b30f5acdea088931a9f') in 0.0121848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/SM_Applesm.prefab
  artifactKey: Guid(37cc5698a53a6bf478be4c88a8e64e9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/SM_Applesm.prefab using Guid(37cc5698a53a6bf478be4c88a8e64e9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8acae6265a633413b10cfd5e4ac046ad') in 1.3877007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Stool_Stone_Furn.prefab
  artifactKey: Guid(caaa734de3fb1bc4788057fd8d11d6f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Stool_Stone_Furn.prefab using Guid(caaa734de3fb1bc4788057fd8d11d6f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c753a764b50d4a4c8d83463a56c34701') in 0.0147741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_02.prefab
  artifactKey: Guid(4c39b1f701ec2de46af88aa81689be7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_02.prefab using Guid(4c39b1f701ec2de46af88aa81689be7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb35bdc20547a6b9c07a9808a4aeb615') in 0.0156016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Light_Hanging_Pole_Furn.prefab
  artifactKey: Guid(9e3740eea4654ff4595607f1fa3a28c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Light_Hanging_Pole_Furn.prefab using Guid(9e3740eea4654ff4595607f1fa3a28c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc9c94ed746856f9c378c02a78a00326') in 0.012104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Cannon_Weap.prefab
  artifactKey: Guid(0023c8b20ba66f440b40cdfb80d40b9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Cannon_Weap.prefab using Guid(0023c8b20ba66f440b40cdfb80d40b9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '674aa12ebd390038fc9986c2365c2c88') in 0.0132753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Pillar_Bldg_01.prefab
  artifactKey: Guid(0d8ae2724d639a34eabff1d210146d9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Pillar_Bldg_01.prefab using Guid(0d8ae2724d639a34eabff1d210146d9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae8e77902e8db028f84fae5d3f5d6d19') in 0.0153215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Chair_Wooden_Furn_04.prefab
  artifactKey: Guid(fb5e4c107a8467043a7603e936d86a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Chair_Wooden_Furn_04.prefab using Guid(fb5e4c107a8467043a7603e936d86a47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c42de4dd8437b65b06e80ceb20ef28e') in 0.0134145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Path_Cobblestone_Env_03.prefab
  artifactKey: Guid(3354a2fee23c9fc45a513927cd492e15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Path_Cobblestone_Env_03.prefab using Guid(3354a2fee23c9fc45a513927cd492e15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86156fbd3a5822c0a221cf9f827a70c5') in 0.0118461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/King.prefab
  artifactKey: Guid(dcbc89efbe5bd174b99e07c76bd0b6bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/King.prefab using Guid(dcbc89efbe5bd174b99e07c76bd0b6bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6158e91ae0d12138584a120f86b9dcee') in 1.6131876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 267

========================================================================
Received Import Request.
  Time since last request: 801.646938 seconds.
  path: Assets/Prefabs/Chunk Prefab.prefab
  artifactKey: Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Prefabs/Chunk Prefab.prefab using Guid(e496cff363a1fa545b0f49d6c55484ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3edcb86085141ab55902c940b7a5bf6') in 0.0116139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 30 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5969 unused Assets / (6.1 MB). Loaded Objects now: 6791.
Memory consumption went from 154.9 MB to 148.7 MB.
Total: 13.466500 ms (FindLiveObjects: 1.081200 ms CreateObjectMapping: 0.414900 ms MarkObjects: 8.574400 ms  DeleteObjects: 3.395000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.630 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.747 seconds
Domain Reload Profiling: 1376ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (147ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (747ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (604ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (186ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 38 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.2 MB). Loaded Objects now: 6684.
Memory consumption went from 163.2 MB to 157.0 MB.
Total: 11.225200 ms (FindLiveObjects: 1.148000 ms CreateObjectMapping: 0.387200 ms MarkObjects: 5.746800 ms  DeleteObjects: 3.942300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.607 seconds
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.582 seconds
Domain Reload Profiling: 1188ms
	BeginReloadAssembly (165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (369ms)
		LoadAssemblies (279ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (133ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (583ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (477ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (276ms)
			ProcessInitializeOnLoadMethodAttributes (37ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.1 MB). Loaded Objects now: 6686.
Memory consumption went from 161.5 MB to 155.4 MB.
Total: 11.098800 ms (FindLiveObjects: 1.124200 ms CreateObjectMapping: 0.711100 ms MarkObjects: 5.774300 ms  DeleteObjects: 3.487900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 297.847486 seconds.
  path: Assets/Scripts/LevelGenerator.cs
  artifactKey: Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/LevelGenerator.cs using Guid(bee8545796cbc7346b0ccd507f1d940a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f42649f89c7308390d738e36f9e4bd4') in 0.0158981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.540 seconds
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1247ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (338ms)
		LoadAssemblies (243ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (80ms)
				TypeCache.ScanAssembly (72ms)
			BuildScriptInfoCaches (69ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (585ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (5.8 MB). Loaded Objects now: 6688.
Memory consumption went from 161.5 MB to 155.7 MB.
Total: 10.036600 ms (FindLiveObjects: 1.003400 ms CreateObjectMapping: 0.408300 ms MarkObjects: 5.191400 ms  DeleteObjects: 3.432500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (6.1 MB). Loaded Objects now: 6688.
Memory consumption went from 161.6 MB to 155.5 MB.
Total: 11.573100 ms (FindLiveObjects: 1.111800 ms CreateObjectMapping: 0.379700 ms MarkObjects: 6.086500 ms  DeleteObjects: 3.994100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.594 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.585 seconds
Domain Reload Profiling: 1179ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (122ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (585ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (262ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.7 MB). Loaded Objects now: 6690.
Memory consumption went from 161.5 MB to 154.8 MB.
Total: 11.187400 ms (FindLiveObjects: 1.063000 ms CreateObjectMapping: 0.436500 ms MarkObjects: 5.369000 ms  DeleteObjects: 4.318200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.546 seconds
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.601 seconds
Domain Reload Profiling: 1145ms
	BeginReloadAssembly (155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (330ms)
		LoadAssemblies (258ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (133ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (118ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (5.9 MB). Loaded Objects now: 6692.
Memory consumption went from 161.5 MB to 155.7 MB.
Total: 10.428600 ms (FindLiveObjects: 1.061400 ms CreateObjectMapping: 0.421800 ms MarkObjects: 5.604600 ms  DeleteObjects: 3.339800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (6.0 MB). Loaded Objects now: 6692.
Memory consumption went from 161.7 MB to 155.7 MB.
Total: 11.794200 ms (FindLiveObjects: 1.121200 ms CreateObjectMapping: 0.418900 ms MarkObjects: 6.572000 ms  DeleteObjects: 3.681200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.590 seconds
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.531 seconds
Domain Reload Profiling: 1122ms
	BeginReloadAssembly (162ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (173ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (531ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (433ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (126ms)
			ProcessInitializeOnLoadAttributes (259ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.2 MB). Loaded Objects now: 6694.
Memory consumption went from 161.6 MB to 155.4 MB.
Total: 11.596000 ms (FindLiveObjects: 1.183500 ms CreateObjectMapping: 0.386200 ms MarkObjects: 6.105300 ms  DeleteObjects: 3.920300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.590 seconds
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.641 seconds
Domain Reload Profiling: 1230ms
	BeginReloadAssembly (168ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (134ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (119ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (641ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (519ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (5.8 MB). Loaded Objects now: 6696.
Memory consumption went from 161.6 MB to 155.8 MB.
Total: 12.540800 ms (FindLiveObjects: 1.082100 ms CreateObjectMapping: 0.479200 ms MarkObjects: 7.720000 ms  DeleteObjects: 3.258500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.611 seconds
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.621 seconds
Domain Reload Profiling: 1233ms
	BeginReloadAssembly (176ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (360ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (164ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (493ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (272ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (5.9 MB). Loaded Objects now: 6698.
Memory consumption went from 161.6 MB to 155.7 MB.
Total: 10.277600 ms (FindLiveObjects: 1.135000 ms CreateObjectMapping: 0.390500 ms MarkObjects: 5.434200 ms  DeleteObjects: 3.317300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 6809.057171 seconds.
  path: Assets/Settings/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '150978b6456685dedb02d43cc963e563') in 0.0544307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 691.289954 seconds.
  path: Packages/com.unity.render-pipelines.universal/Textures/DebugFont.tga
  artifactKey: Guid(26a413214480ef144b2915d6ff4d0beb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.universal/Textures/DebugFont.tga using Guid(26a413214480ef144b2915d6ff4d0beb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '857cb34589f756901cddab1138be332d') in 0.0269273 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.555 seconds
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.558 seconds
Domain Reload Profiling: 1114ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (352ms)
		LoadAssemblies (247ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (85ms)
				TypeCache.ScanAssembly (75ms)
			BuildScriptInfoCaches (71ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (558ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (445ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (259ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5976 unused Assets / (5.8 MB). Loaded Objects now: 6702.
Memory consumption went from 161.7 MB to 156.0 MB.
Total: 9.840700 ms (FindLiveObjects: 1.029400 ms CreateObjectMapping: 0.344300 ms MarkObjects: 5.170100 ms  DeleteObjects: 3.295900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.590 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.594 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (283ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (135ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (470ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 29 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.5 MB). Loaded Objects now: 6704.
Memory consumption went from 161.8 MB to 154.3 MB.
Total: 15.410300 ms (FindLiveObjects: 1.246900 ms CreateObjectMapping: 1.108000 ms MarkObjects: 7.128800 ms  DeleteObjects: 5.925800 ms)

Prepare: number of updated asset objects reloaded= 0
