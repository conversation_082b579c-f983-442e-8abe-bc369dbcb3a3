using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Utility script to help fix Unity Editor issues with null references and missing objects
/// </summary>
public class EditorFixer : MonoBehaviour
{
#if UNITY_EDITOR
    [MenuItem("Tools/Fix Editor Issues")]
    public static void FixEditorIssues()
    {
        Debug.Log("Starting Editor Fix Process...");
        
        // Clear console
        var assembly = System.Reflection.Assembly.GetAssembly(typeof(SceneView));
        var type = assembly.GetType("UnityEditor.LogEntries");
        var method = type.GetMethod("Clear");
        method.Invoke(new object(), null);
        
        // Force refresh of all assets
        AssetDatabase.Refresh();
        
        // Clear selection to avoid inspector issues
        Selection.activeObject = null;
        
        // Force garbage collection
        System.GC.Collect();
        
        // Reimport all scripts
        AssetDatabase.ImportAsset("Assets/Scripts", ImportAssetOptions.ImportRecursive);
        
        Debug.Log("Editor Fix Process Complete. Try selecting objects again.");
    }
    
    [MenuItem("Tools/Validate Scene Objects")]
    public static void ValidateSceneObjects()
    {
        Debug.Log("Validating Scene Objects...");
        
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        int nullCount = 0;
        int validCount = 0;
        
        foreach (GameObject obj in allObjects)
        {
            if (obj == null)
            {
                nullCount++;
            }
            else
            {
                validCount++;
                // Check for missing components
                Component[] components = obj.GetComponents<Component>();
                foreach (Component comp in components)
                {
                    if (comp == null)
                    {
                        Debug.LogWarning($"Missing component found on GameObject: {obj.name}", obj);
                    }
                }
            }
        }
        
        Debug.Log($"Scene Validation Complete. Valid Objects: {validCount}, Null Objects: {nullCount}");
    }
    
    [MenuItem("Tools/Clear Selection")]
    public static void ClearSelection()
    {
        Selection.activeObject = null;
        Debug.Log("Selection cleared.");
    }
#endif
}
