﻿Pass
{
    Name "DBufferProjectorVFX"
    Tags
    {
        "LightMode" = "DBufferProjectorVFX"
    }

    // Render State
    Cull Front
    Blend 0 SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
    Blend 1 SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
    Blend 2 SrcAlpha OneMinusSrcAlpha, Zero OneMinusSrcAlpha
    ZTest Greater
    ZWrite Off

    ColorMask ${VFXDBufferColorMask0}
    ColorMask ${VFXDBufferColorMask1} 1
    ColorMask ${VFXDBufferColorMask2} 2

    HLSLPROGRAM

    // Pragmas
    #pragma target 4.5
    #pragma exclude_renderers gles3 glcore
    #pragma editor_sync_compilation

    // Keywords
    #pragma multi_compile_fragment _ _DBUFFER_MRT1 _DBUFFER_MRT2 _DBUFFER_MRT3
    #include_with_pragmas "Packages/com.unity.render-pipelines.core/ShaderLibrary/FoveatedRenderingKeywords.hlsl"
    #pragma multi_compile _ _DECAL_LAYERS

    ${VFXIncludeRP("VFXDecalVaryings.template")}
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/NormalSurfaceGradient.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DecalInput.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DBuffer.hlsl"
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/FoveatedRendering.hlsl"

    struct ps_input
    {
        float4 pos : SV_POSITION;
        ${VFXURPDecalDeclareVaryings}
        UNITY_VERTEX_OUTPUT_STEREO
    };

    ${VFXURPDecalVaryingsMacros}

    ${VFXBegin:VFXVertexAdditionalProcess}
    ${VFXURPDecalFillVaryings}
    ${VFXEnd}

    ${VFXInclude("Shaders/ParticleHexahedron/Pass.template")}
    #define SHADERPASS SHADERPASS_DBUFFER_PROJECTOR
    #if defined(DECAL_ANGLE_FADE)
    #define DECAL_LOAD_NORMAL
    #endif

    ${VFXIncludeRP("VFXDecal.template")}

    #define VFXComputePixelOutputToDBuffer(i,outDBuffer) \
    { \
	    DecalSurfaceData surfaceData; \
        ZERO_INITIALIZE(DecalSurfaceData, surfaceData); \
        PositionInputs posInput; \
        ZERO_INITIALIZE(PositionInputs, posInput); \
        half3 receiverNormalWS; \
        VFXGetSurfaceDecalData(surfaceData, posInput, receiverNormalWS, i); \
     \
        ENCODE_INTO_DBUFFER(surfaceData, outDBuffer); \
    }

    #pragma fragment frag
    void frag(ps_input i, OUTPUT_DBUFFER(outDBuffer))
    {
        UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i)
        VFXComputePixelOutputToDBuffer(i,outDBuffer);
    }
    ENDHLSL
}




