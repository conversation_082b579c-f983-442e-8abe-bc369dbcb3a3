Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.53f1 (283510a092d9) revision 2635024'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 64661 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-06T21:19:18Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.53f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
-logFile
Logs/AssetImportWorker4.log
-srvPort
51478
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [3172]  Target information:

Player connection [3172]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 678636279 [EditorId] 678636279 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [3172]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 678636279 [EditorId] 678636279 [Version] 1048832 [Id] WindowsEditor(7,AlePC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [3172] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.53f1 (283510a092d9)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Jogos Unity/AfterDark/Royal Run/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4090 (ID=0x2684)
    Vendor:   NVIDIA
    VRAM:     22612 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56268
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.53f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.002829 seconds.
- Loaded All Assemblies, in  0.278 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 173 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.449 seconds
Domain Reload Profiling: 725ms
	BeginReloadAssembly (93ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (114ms)
		LoadAssemblies (92ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (111ms)
			TypeCache.Refresh (110ms)
				TypeCache.ScanAssembly (101ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (449ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (414ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (230ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (44ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.541 seconds
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.520 seconds
Domain Reload Profiling: 1057ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (343ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (115ms)
				TypeCache.ScanAssembly (104ms)
			BuildScriptInfoCaches (33ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (521ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (405ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (5.9 MB). Loaded Objects now: 6634.
Memory consumption went from 155.5 MB to 149.6 MB.
Total: 10.395800 ms (FindLiveObjects: 1.197200 ms CreateObjectMapping: 0.591700 ms MarkObjects: 5.541700 ms  DeleteObjects: 3.064200 ms)

========================================================================
Received Import Request.
  Time since last request: 30153.123830 seconds.
  path: Assets/Imported Assets
  artifactKey: Guid(5390f65f0ddbe8147a640a88de127c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets using Guid(5390f65f0ddbe8147a640a88de127c93) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d057a05da662368847e5b7852bed103') in 0.0037631 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.783169 seconds.
  path: Assets/Imported Assets/GDTV Assets
  artifactKey: Guid(ddb0ead78b84c794ba7608be25dde30b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets using Guid(ddb0ead78b84c794ba7608be25dde30b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad975b0cc49848c99cb2bcae9aecd16f') in 0.0003638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.992506 seconds.
  path: Assets/Imported Assets/GDTV Assets/Animations
  artifactKey: Guid(f328300395ce2664a86e7dcc58db76a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Animations using Guid(f328300395ce2664a86e7dcc58db76a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '490615526b3ece1e94ffab76f854a0d3') in 0.0004162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 14.129706 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs
  artifactKey: Guid(8340c32a4a00fa8439d457737dd28dc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs using Guid(8340c32a4a00fa8439d457737dd28dc2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '012f735214a9f6cc8616c9495a2b95cc') in 0.0004519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.041562 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Banner_Pole_Prop.prefab
  artifactKey: Guid(dd319dcc6b26fce498151719fb190433) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Banner_Pole_Prop.prefab using Guid(dd319dcc6b26fce498151719fb190433) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9c9de5e276b92360220bc4ddd0b9309') in 1.9604746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Sign_Furn.prefab
  artifactKey: Guid(5e55e605674404f4ba346895e2675cbb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Sign_Furn.prefab using Guid(5e55e605674404f4ba346895e2675cbb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20b2014726bd3fb1e22becf4682b5ce6') in 0.0159708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Crate_Wooden_Prop.prefab
  artifactKey: Guid(1dbc3380871ee554db4b0105cc963764) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Crate_Wooden_Prop.prefab using Guid(1dbc3380871ee554db4b0105cc963764) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f5b4ed5ffc7529a433622bd55bfa986') in 0.0131751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/SM_Powerup_Coin_Gold.prefab
  artifactKey: Guid(ab7079beca09c4a45a24d22df07e346f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/SM_Powerup_Coin_Gold.prefab using Guid(ab7079beca09c4a45a24d22df07e346f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '387b885010dab1d2cdc2ce16f8ff92d7') in 1.5534685 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Fence_Lattice_Env_3.prefab
  artifactKey: Guid(1656d845b8440f7499036e22fd8a8999) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Fence_Lattice_Env_3.prefab using Guid(1656d845b8440f7499036e22fd8a8999) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3eaa6a28756761f5a835f0cb097651fd') in 0.0152027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Flag_Prop.prefab
  artifactKey: Guid(68a853dabcdaa3445ab666d8badab23f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Flag_Prop.prefab using Guid(68a853dabcdaa3445ab666d8badab23f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74669dede9cee836e54c1fb418368062') in 0.0154001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Wooden_Beam_Prop_07.prefab
  artifactKey: Guid(1344acf7c5f71a4489cc0546666b72a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Wooden_Beam_Prop_07.prefab using Guid(1344acf7c5f71a4489cc0546666b72a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9dfa331b82cfcc549d36fc46600b2d1d') in 0.012655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Rock_Small_Env_03.prefab
  artifactKey: Guid(ea25b10905178f7438b99d067166f765) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Rock_Small_Env_03.prefab using Guid(ea25b10905178f7438b99d067166f765) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7340d30cb9a6a7e2698e79cfcf68012') in 0.0130465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Tree_Stump_Vege_01.prefab
  artifactKey: Guid(0fea0fb09c334714c91e6341b0890375) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Tree_Stump_Vege_01.prefab using Guid(0fea0fb09c334714c91e6341b0890375) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b75c48de24fc106093910d10d4904879') in 0.0131474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_03.prefab
  artifactKey: Guid(b4a8bcfa9b3dbab48bfb90252163fab0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Bench_Wooden_Furn_03.prefab using Guid(b4a8bcfa9b3dbab48bfb90252163fab0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd60704cf89e77f37bdf6f1cb29290f4f') in 0.0147029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Imported Assets/GDTV Assets/Prefabs/Mossy_Rock_Env_01.prefab
  artifactKey: Guid(94e6a88d7014ff54386296c2929cb89e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Imported Assets/GDTV Assets/Prefabs/Mossy_Rock_Env_01.prefab using Guid(94e6a88d7014ff54386296c2929cb89e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '467c61ce60383dea3ddacf717771f064') in 0.0152656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0