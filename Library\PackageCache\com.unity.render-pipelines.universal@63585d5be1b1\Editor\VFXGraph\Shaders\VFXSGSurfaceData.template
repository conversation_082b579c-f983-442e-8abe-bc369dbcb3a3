SurfaceData surface;
surface = (SurfaceData)0;

surface.occlusion = 1.0f;

#if HAS_SHADERGRAPH_PARAM_ALPHA
    surface.alpha = OUTSG.${SHADERGRAPH_PARAM_ALPHA};
    VFXClipFragmentColor(surface.alpha, i);
#endif

#if HAS_SHADERGRAPH_PARAM_SMOOTHNESS
    surface.smoothness = OUTSG.${SHADERGRAPH_PARAM_SMOOTHNESS};
#endif

#if HAS_SHADERGRAPH_PARAM_METALLIC
    surface.metallic = OUTSG.${SHADERGRAPH_PARAM_METALLIC};
#endif

#if HAS_SHADERGRAPH_PARAM_BASECOLOR
    surface.albedo = OUTSG.${SHADERGRAPH_PARAM_BASECOLOR};
#endif

    surface.normalTS = float3(0, 0, 1);
#if HAS_SHADERGRAPH_PARAM_NORMALTS
    float3 n =  OUTSG.${SHADERGRAPH_PARAM_NORMALTS};
    normalWS = mul(n,tbn);
#endif

#if HAS_SHADERGRAPH_PARAM_EMISSION
    surface.emission = OUTSG.${SHADERGRAPH_PARAM_EMISSION};
#endif
