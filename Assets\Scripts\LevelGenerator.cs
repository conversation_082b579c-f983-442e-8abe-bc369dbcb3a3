using System.Collections.Generic;
using UnityEngine;

public class LevelGenerator : MonoBehaviour
{
    [SerializeField] GameObject chunkPrefab;
    [SerializeField] int startingChunksAmount = 12;

    [SerializeField] Transform chunkParent;
    [SerializeField] float chunkLength = 10f;

    [SerializeField] float moveSpeed = 10f;

     

    List<GameObject> chunks = new List<GameObject>();


    void Start()
    {
        SpawnChanks();

    }

    void SpawnChanks()
    {
        for (int i = 0; i < startingChunksAmount; i++)
        {

            float spawnPositionZ;

            spawnPositionZ = CalculateSpawnPosition(i);

            Vector3 chunkSpawnPos = new Vector3(transform.position.x, transform.position.y, spawnPositionZ);
            GameObject newChunk = Instantiate(chunkPrefab, chunkSpawnPos, Quaternion.identity, chunkParent);


            chunks[i] = newChunk;
        }
    }

    float CalculateSpawnPosition(int i)
    {
        float spawnPositionZ;
        if (i == 0)
        {
            spawnPositionZ = transform.position.z;
        }
        else
        {
            spawnPositionZ = transform.position.z + chunkLength * i;
        }

        return spawnPositionZ;
    }

    void MoveChunks()
    { 
        for (int i = 0; i < chunks.Count; i++)
        {
            chunks[i].transform.Translate(-transform.forward * moveSpeed * Time.deltaTime);
        }
    }
    void Update()
    {
        MoveChunks();
    }

}
