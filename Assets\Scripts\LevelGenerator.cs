using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class LevelGenerator : MonoBehaviour
{
    [SerializeField] GameObject chunkPrefab;
    [SerializeField] int startingChunksAmount = 12;

    [SerializeField] Transform chunkParent;
    [SerializeField] float chunkLength = 10f;

    [SerializeField] float moveSpeed = 10f;

     

    List<GameObject> chunks = new List<GameObject>();


    void Start()
    {
        if (ValidateReferences())
        {
            SpawnChanks();
        }
    }

    bool ValidateReferences()
    {
        bool isValid = true;

        if (chunkPrefab == null)
        {
            Debug.LogError("LevelGenerator: chunkPrefab is not assigned in the inspector!", this);
            isValid = false;
        }

        if (chunkParent == null)
        {
            Debug.LogWarning("LevelGenerator: chunkParent is not assigned. Chunks will be spawned without a parent.", this);
        }

        return isValid;
    }

    void SpawnChanks()
    {
        // Check for null references before spawning
        if (chunkPrefab == null)
        {
            Debug.LogError("LevelGenerator: chunkPrefab is not assigned!");
            return;
        }

        // Clear existing chunks and initialize the list
        chunks.Clear();

        for (int i = 0; i < startingChunksAmount; i++)
        {
            float spawnPositionZ = CalculateSpawnPosition(i);
            Vector3 chunkSpawnPos = new Vector3(transform.position.x, transform.position.y, spawnPositionZ);
            GameObject newChunk = Instantiate(chunkPrefab, chunkSpawnPos, Quaternion.identity, chunkParent);

            // Add to list instead of trying to assign to index
            chunks.Add(newChunk);
        }
    }

    float CalculateSpawnPosition(int i)
    {
        float spawnPositionZ;
        if (i == 0)
        {
            spawnPositionZ = transform.position.z;
        }
        else
        {
            spawnPositionZ = transform.position.z + chunkLength * i;
        }

        return spawnPositionZ;
    }

    void MoveChunks()
    {
        for (int i = 0; i < chunks.Count; i++)
        {
            // Check if chunk still exists (not destroyed)
            if (chunks[i] != null)
            {
                Vector3 movement = -transform.forward * (moveSpeed * Time.deltaTime);
                chunks[i].transform.Translate(movement);
            }
        }
    }
    void Update()
    {
        MoveChunks();
    }

}
